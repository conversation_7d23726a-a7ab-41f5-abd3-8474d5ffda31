import { z } from 'zod';

// Customer validation schemas
export const createCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  sessionCredits: z.number().int().min(0, 'Session credits must be non-negative'),
});

export const updateCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters').optional(),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  sessionCredits: z.number().int().min(0, 'Session credits must be non-negative').optional(),
});

export const customerQuerySchema = z.object({
  search: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['name', 'email', 'sessionCredits', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Purchase validation schemas
export const createPurchaseSchema = z.object({
  sessionsPurchased: z.number().int().min(1, 'Must purchase at least 1 session'),
  amountPaid: z.number().min(0, 'Amount paid must be non-negative'),
  paymentStatus: z.enum(['completed', 'pending', 'failed']),
});

export const purchaseQuerySchema = z.object({
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['purchaseDate', 'amountPaid', 'sessionsPurchased']).default('purchaseDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// API response schemas
export const customerResponseSchema = z.object({
  id: z.string().uuid(),
  trainerId: z.string().uuid(),
  name: z.string(),
  email: z.string().nullable(),
  phone: z.string().nullable(),
  sessionCredits: z.number().int(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const purchaseResponseSchema = z.object({
  id: z.string().uuid(),
  customerId: z.string().uuid(),
  sessionsPurchased: z.number().int(),
  amountPaid: z.string(), // Decimal comes as string from DB
  purchaseDate: z.date(),
  paymentStatus: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    pagination: z.object({
      total: z.number().int(),
      limit: z.number().int(),
      offset: z.number().int(),
      hasMore: z.boolean(),
    }),
  });

// Error response schema
export const errorResponseSchema = z.object({
  error: z.string(),
  message: z.string(),
  details: z.record(z.any()).optional(),
});

// Type exports
export type CreateCustomerInput = z.infer<typeof createCustomerSchema>;
export type UpdateCustomerInput = z.infer<typeof updateCustomerSchema>;
export type CustomerQueryInput = z.infer<typeof customerQuerySchema>;
export type CreatePurchaseInput = z.infer<typeof createPurchaseSchema>;
export type PurchaseQueryInput = z.infer<typeof purchaseQuerySchema>;
export type CustomerResponse = z.infer<typeof customerResponseSchema>;
export type PurchaseResponse = z.infer<typeof purchaseResponseSchema>;
export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
};
export type ErrorResponse = z.infer<typeof errorResponseSchema>;
