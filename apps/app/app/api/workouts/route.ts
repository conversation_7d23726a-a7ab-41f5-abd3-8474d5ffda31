import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and, ilike, asc, desc, count, gte, lte, sql } from 'drizzle-orm';
import {
  createWorkoutSchema,
  workoutQuerySchema,
  type CreateWorkoutInput,
  type WorkoutQueryInput,
  type PaginatedResponse,
  type WorkoutResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/workouts - List workouts with search and pagination
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const queryValidation = validateQueryParams(request, workoutQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, status, startDate, endDate, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(workouts.trainerId, trainerId)];

    if (search) {
      whereConditions.push(ilike(workouts.title, `%${search}%`));
    }

    if (status) {
      whereConditions.push(eq(workouts.status, status));
    }

    if (startDate) {
      whereConditions.push(gte(workouts.startTime, new Date(startDate)));
    }

    if (endDate) {
      whereConditions.push(lte(workouts.endTime, new Date(endDate)));
    }

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(workouts)
      .where(and(...whereConditions));

    // Get workouts with participant count
    const workoutList = await db
      .select({
        id: workouts.id,
        trainerId: workouts.trainerId,
        title: workouts.title,
        description: workouts.description,
        startTime: workouts.startTime,
        endTime: workouts.endTime,
        minParticipants: workouts.minParticipants,
        maxParticipants: workouts.maxParticipants,
        status: workouts.status,
        location: workouts.location,
        createdAt: workouts.createdAt,
        updatedAt: workouts.updatedAt,
        participantCount: sql<number>`COALESCE(COUNT(${workoutParticipants.id}), 0)`,
      })
      .from(workouts)
      .leftJoin(workoutParticipants, eq(workouts.id, workoutParticipants.workoutId))
      .where(and(...whereConditions))
      .groupBy(workouts.id)
      .orderBy(
        sortOrder === 'desc'
          ? desc(workouts[sortBy as keyof typeof workouts])
          : asc(workouts[sortBy as keyof typeof workouts])
      )
      .limit(limit)
      .offset(offset);

    const response: PaginatedResponse<WorkoutResponse> = {
      data: workoutList.map((workout) => ({
        ...workout,
        participantCount: Number(workout.participantCount),
      })),
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/workouts - Create new workout
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createWorkoutSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const workoutData = bodyValidation.data;

    // Create workout
    const [newWorkout] = await db
      .insert(workouts)
      .values({
        trainerId,
        title: workoutData.title,
        description: workoutData.description || null,
        startTime: new Date(workoutData.startTime),
        endTime: new Date(workoutData.endTime),
        minParticipants: workoutData.minParticipants,
        maxParticipants: workoutData.maxParticipants,
        location: workoutData.location || null,
      })
      .returning();

    if (!newWorkout) {
      throw new Error('Failed to create workout');
    }

    const response: WorkoutResponse = {
      id: newWorkout.id,
      trainerId: newWorkout.trainerId,
      title: newWorkout.title,
      description: newWorkout.description,
      startTime: newWorkout.startTime,
      endTime: newWorkout.endTime,
      minParticipants: newWorkout.minParticipants,
      maxParticipants: newWorkout.maxParticipants,
      status: newWorkout.status,
      location: newWorkout.location,
      createdAt: newWorkout.createdAt,
      updatedAt: newWorkout.updatedAt,
      participants: [],
      participantCount: 0,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
