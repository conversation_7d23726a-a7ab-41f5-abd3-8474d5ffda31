import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateWorkoutSchema, type UpdateWorkoutInput, type WorkoutResponse } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/workouts/[id] - Get single workout with participants
export const GET = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Get workout with participants
    const [workout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!workout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Get participants
    const participants = await db
      .select({
        id: workoutParticipants.id,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.workoutId, id));

    const response: WorkoutResponse = {
      ...workout,
      participants,
      participantCount: participants.length,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error, 'Failed to fetch workout');
  }
});

// PUT /api/workouts/[id] - Update workout
export const PUT = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, updateWorkoutSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const updateData = bodyValidation.data;

    // Check if workout exists and belongs to trainer
    const [existingWorkout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!existingWorkout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Prepare update data
    const updateValues: any = {
      updatedAt: new Date(),
    };

    if (updateData.title !== undefined) updateValues.title = updateData.title;
    if (updateData.description !== undefined) updateValues.description = updateData.description || null;
    if (updateData.startTime !== undefined) updateValues.startTime = new Date(updateData.startTime);
    if (updateData.endTime !== undefined) updateValues.endTime = new Date(updateData.endTime);
    if (updateData.minParticipants !== undefined) updateValues.minParticipants = updateData.minParticipants;
    if (updateData.maxParticipants !== undefined) updateValues.maxParticipants = updateData.maxParticipants;
    if (updateData.status !== undefined) updateValues.status = updateData.status;
    if (updateData.location !== undefined) updateValues.location = updateData.location || null;

    // Update workout
    const [updatedWorkout] = await db.update(workouts).set(updateValues).where(eq(workouts.id, id)).returning();

    // Get participants for response
    const participants = await db
      .select({
        id: workoutParticipants.id,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.workoutId, id));

    const response: WorkoutResponse = {
      ...updatedWorkout,
      participants,
      participantCount: participants.length,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error, 'Failed to update workout');
  }
});

// DELETE /api/workouts/[id] - Delete workout
export const DELETE = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Check if workout exists and belongs to trainer
    const [existingWorkout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!existingWorkout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Delete workout participants first (foreign key constraint)
    await db.delete(workoutParticipants).where(eq(workoutParticipants.workoutId, id));

    // Delete workout
    await db.delete(workouts).where(eq(workouts.id, id));

    return createSuccessResponse({ message: 'Workout deleted successfully' });
  } catch (error) {
    return handleApiError(error, 'Failed to delete workout');
  }
});
