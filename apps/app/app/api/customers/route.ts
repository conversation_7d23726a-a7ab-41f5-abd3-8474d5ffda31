import { NextRequest } from 'next/server';
import { db, customers } from '@workspace/auth/server';
import { eq, and, ilike, asc, desc, count } from 'drizzle-orm';
import {
  createCustomerSchema,
  customerQuerySchema,
  type CreateCustomerInput,
  type CustomerQueryInput,
  type PaginatedResponse,
  type CustomerResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/customers - List customers with search and pagination
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const queryValidation = validateQueryParams(request, customerQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(customers.trainerId, trainerId)];
    
    if (search) {
      whereConditions.push(
        ilike(customers.name, `%${search}%`)
      );
    }

    // Build order by
    const orderByColumn = customers[sortBy as keyof typeof customers];
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(customers)
      .where(and(...whereConditions));

    const total = totalResult.count;

    // Get customers
    const customerList = await db
      .select()
      .from(customers)
      .where(and(...whereConditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit)
      .offset(offset);

    const response: PaginatedResponse<CustomerResponse> = {
      data: customerList.map(customer => ({
        id: customer.id,
        trainerId: customer.trainerId,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        sessionCredits: customer.sessionCredits,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      })),
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/customers - Create new customer
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createCustomerSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const customerData = bodyValidation.data;

    // Create customer
    const [newCustomer] = await db
      .insert(customers)
      .values({
        trainerId,
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        sessionCredits: customerData.sessionCredits,
      })
      .returning();

    const response: CustomerResponse = {
      id: newCustomer.id,
      trainerId: newCustomer.trainerId,
      name: newCustomer.name,
      email: newCustomer.email,
      phone: newCustomer.phone,
      sessionCredits: newCustomer.sessionCredits,
      createdAt: newCustomer.createdAt,
      updatedAt: newCustomer.updatedAt,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
